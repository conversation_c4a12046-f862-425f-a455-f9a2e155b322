
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for ailex_website/client/src/components/DashboardPreview.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> / <a href="index.html">ailex_website/client/src/components</a> DashboardPreview.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/199</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/199</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >import { motion } from "framer-motion";<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
import { type State } from "@/types";
<span class="cstat-no" title="statement not covered" >import { useIsMobile } from "@/hooks/use-mobile";</span>
&nbsp;
type DashboardPreviewProps = {
  state?: State;
};
&nbsp;
<span class="cstat-no" title="statement not covered" >export default function DashboardPreview({ state }: DashboardPreviewProps) {</span>
<span class="cstat-no" title="statement not covered" >  const isMobile = useIsMobile();</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;motion.div</span>
<span class="cstat-no" title="statement not covered" >      className="relative flex justify-center items-center perspective-[2000px]"</span>
<span class="cstat-no" title="statement not covered" >      initial={{ opacity: 0, y: 20 }}</span>
<span class="cstat-no" title="statement not covered" >      animate={{ opacity: 1, y: 0 }}</span>
<span class="cstat-no" title="statement not covered" >      transition={{ duration: 0.5 }}</span>
    &gt;
      {/* Background Gradient for 3D effect */}
<span class="cstat-no" title="statement not covered" >      &lt;div className="absolute inset-0 bg-gradient-to-b from-white via-blue-50 to-blue-100 opacity-60 blur-2xl rounded-full"&gt;&lt;/div&gt;</span>
&nbsp;
      {/* Card wrapper with perspective */}
<span class="cstat-no" title="statement not covered" >      &lt;motion.div</span>
<span class="cstat-no" title="statement not covered" >        className={`relative ${isMobile ? "" : "animate-float-slow"}`}</span>
<span class="cstat-no" title="statement not covered" >        whileHover={{</span>
<span class="cstat-no" title="statement not covered" >          scale: isMobile ? 1.02 : 0.97,</span>
<span class="cstat-no" title="statement not covered" >          y: isMobile ? -3 : -2,</span>
<span class="cstat-no" title="statement not covered" >          transition: { duration: 0.3 },</span>
<span class="cstat-no" title="statement not covered" >        }}</span>
      &gt;
        {/* Glow effect behind the card */}
<span class="cstat-no" title="statement not covered" >        &lt;div className="absolute inset-0 bg-blue-400/20 rounded-3xl blur-xl transform -translate-y-2"&gt;&lt;/div&gt;</span>
&nbsp;
        {/* Main card container */}
<span class="cstat-no" title="statement not covered" >        &lt;div className="bg-white/70 backdrop-blur-md rounded-3xl shadow-[0px_30px_60px_rgba(0,0,0,0.2)] p-6 w-full max-w-sm relative"&gt;</span>
          {/* Decorative elements */}
<span class="cstat-no" title="statement not covered" >          &lt;div className="absolute -top-2 -right-2 w-16 h-16 bg-blue-50 rounded-full blur-lg opacity-70"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="absolute bottom-10 -left-4 w-10 h-10 bg-[#B8FF5C]/30 rounded-full blur-md"&gt;&lt;/div&gt;</span>
&nbsp;
          {/* Header section */}
<span class="cstat-no" title="statement not covered" >          &lt;div className="flex items-center justify-between mb-6 relative z-10"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-full w-10 h-10 flex items-center justify-center text-white font-bold shadow-md"&gt;</span>
                AI
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="ml-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-sm font-semibold text-gray-800"&gt;</span>
                  AiLex Dashboard
<span class="cstat-no" title="statement not covered" >                &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-xs text-gray-500"&gt;</span>
                  Firm: Smith &amp; Associates
<span class="cstat-no" title="statement not covered" >                &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="px-3 py-1 bg-[#B8FF5C] bg-opacity-90 text-navy text-xs font-semibold rounded-full shadow-sm flex items-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="w-2 h-2 bg-green-500 rounded-full mr-1.5 animate-pulse"&gt;&lt;/span&gt;</span>
              Ready
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
&nbsp;
          {/* Active matters card */}
<span class="cstat-no" title="statement not covered" >          &lt;div className="bg-white rounded-xl shadow-sm p-4 mb-5 border border-gray-100"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex justify-between items-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-1"&gt;</span>
                  Active Matters
<span class="cstat-no" title="statement not covered" >                &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent"&gt;</span>
                  14
<span class="cstat-no" title="statement not covered" >                &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="h-12 w-12 rounded-lg bg-blue-50 flex items-center justify-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;svg</span>
<span class="cstat-no" title="statement not covered" >                  xmlns="http://www.w3.org/2000/svg"</span>
<span class="cstat-no" title="statement not covered" >                  className="h-6 w-6 text-blue-500"</span>
<span class="cstat-no" title="statement not covered" >                  fill="none"</span>
<span class="cstat-no" title="statement not covered" >                  viewBox="0 0 24 24"</span>
<span class="cstat-no" title="statement not covered" >                  stroke="currentColor"</span>
                &gt;
<span class="cstat-no" title="statement not covered" >                  &lt;path</span>
<span class="cstat-no" title="statement not covered" >                    strokeLinecap="round"</span>
<span class="cstat-no" title="statement not covered" >                    strokeLinejoin="round"</span>
<span class="cstat-no" title="statement not covered" >                    strokeWidth={2}</span>
<span class="cstat-no" title="statement not covered" >                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"</span>
<span class="cstat-no" title="statement not covered" >                  /&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="mt-2 flex items-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="text-xs text-green-500 font-medium flex items-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;svg</span>
<span class="cstat-no" title="statement not covered" >                  xmlns="http://www.w3.org/2000/svg"</span>
<span class="cstat-no" title="statement not covered" >                  className="h-3 w-3 mr-1"</span>
<span class="cstat-no" title="statement not covered" >                  viewBox="0 0 20 20"</span>
<span class="cstat-no" title="statement not covered" >                  fill="currentColor"</span>
                &gt;
<span class="cstat-no" title="statement not covered" >                  &lt;path</span>
<span class="cstat-no" title="statement not covered" >                    fillRule="evenodd"</span>
<span class="cstat-no" title="statement not covered" >                    d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z"</span>
<span class="cstat-no" title="statement not covered" >                    clipRule="evenodd"</span>
<span class="cstat-no" title="statement not covered" >                  /&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/svg&gt;</span>
                +2 this week
<span class="cstat-no" title="statement not covered" >              &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="ml-auto flex space-x-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="w-6 h-2 rounded-full bg-blue-400"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="w-3 h-2 rounded-full bg-gray-200"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="w-2 h-2 rounded-full bg-gray-200"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
&nbsp;
          {/* Activity and schedule section */}
<span class="cstat-no" title="statement not covered" >          &lt;div className="grid grid-cols-1 gap-4 mb-5"&gt;</span>
            {/* Activity section */}
<span class="cstat-no" title="statement not covered" >            &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="flex justify-between items-center mb-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-xs uppercase font-semibold text-gray-500 tracking-wider"&gt;</span>
                  Recent Activity
<span class="cstat-no" title="statement not covered" >                &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;span className="text-xs text-blue-500 font-medium hover:text-blue-600 cursor-pointer"&gt;</span>
                  See all
<span class="cstat-no" title="statement not covered" >                &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >              &lt;div className="space-y-2.5"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="bg-white rounded-lg p-3 shadow-sm border border-gray-50 hover:border-blue-100 transition-all duration-200"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="flex items-start"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="h-6 w-6 rounded bg-blue-50 flex items-center justify-center mr-2 mt-0.5"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;svg</span>
<span class="cstat-no" title="statement not covered" >                        xmlns="http://www.w3.org/2000/svg"</span>
<span class="cstat-no" title="statement not covered" >                        className="h-3.5 w-3.5 text-blue-500"</span>
<span class="cstat-no" title="statement not covered" >                        viewBox="0 0 20 20"</span>
<span class="cstat-no" title="statement not covered" >                        fill="currentColor"</span>
                      &gt;
<span class="cstat-no" title="statement not covered" >                        &lt;path</span>
<span class="cstat-no" title="statement not covered" >                          fillRule="evenodd"</span>
<span class="cstat-no" title="statement not covered" >                          d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"</span>
<span class="cstat-no" title="statement not covered" >                          clipRule="evenodd"</span>
<span class="cstat-no" title="statement not covered" >                        /&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;p className="text-sm font-medium text-gray-800"&gt;</span>
                        Document Analysis
<span class="cstat-no" title="statement not covered" >                      &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;p className="text-xs text-gray-400"&gt;</span>
                        Bailey v. State.pdf
<span class="cstat-no" title="statement not covered" >                      &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                &lt;div className="bg-white rounded-lg p-3 shadow-sm border border-gray-50 hover:border-blue-100 transition-all duration-200"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="flex items-start"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="h-6 w-6 rounded bg-blue-50 flex items-center justify-center mr-2 mt-0.5"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;svg</span>
<span class="cstat-no" title="statement not covered" >                        xmlns="http://www.w3.org/2000/svg"</span>
<span class="cstat-no" title="statement not covered" >                        className="h-3.5 w-3.5 text-blue-500"</span>
<span class="cstat-no" title="statement not covered" >                        viewBox="0 0 20 20"</span>
<span class="cstat-no" title="statement not covered" >                        fill="currentColor"</span>
                      &gt;
<span class="cstat-no" title="statement not covered" >                        &lt;path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;p className="text-sm font-medium text-gray-800"&gt;</span>
                        Client Intake
<span class="cstat-no" title="statement not covered" >                      &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;p className="text-xs text-gray-400"&gt;Rodriguez Estate&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
&nbsp;
            {/* Upcoming section */}
<span class="cstat-no" title="statement not covered" >            &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="flex justify-between items-center mb-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-xs uppercase font-semibold text-gray-500 tracking-wider"&gt;</span>
                  Upcoming
<span class="cstat-no" title="statement not covered" >                &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;span className="text-xs text-blue-500 font-medium hover:text-blue-600 cursor-pointer"&gt;</span>
                  Schedule
<span class="cstat-no" title="statement not covered" >                &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >              &lt;div className="space-y-2.5"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="bg-white rounded-lg p-3 shadow-sm border border-gray-50 hover:border-blue-100 transition-all duration-200"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="flex items-start"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="h-6 w-6 rounded bg-orange-50 flex items-center justify-center mr-2 mt-0.5"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;svg</span>
<span class="cstat-no" title="statement not covered" >                        xmlns="http://www.w3.org/2000/svg"</span>
<span class="cstat-no" title="statement not covered" >                        className="h-3.5 w-3.5 text-orange-500"</span>
<span class="cstat-no" title="statement not covered" >                        viewBox="0 0 20 20"</span>
<span class="cstat-no" title="statement not covered" >                        fill="currentColor"</span>
                      &gt;
<span class="cstat-no" title="statement not covered" >                        &lt;path</span>
<span class="cstat-no" title="statement not covered" >                          fillRule="evenodd"</span>
<span class="cstat-no" title="statement not covered" >                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"</span>
<span class="cstat-no" title="statement not covered" >                          clipRule="evenodd"</span>
<span class="cstat-no" title="statement not covered" >                        /&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;p className="text-sm font-medium text-gray-800"&gt;</span>
                        Filing Deadline
<span class="cstat-no" title="statement not covered" >                      &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;p className="text-xs text-gray-400"&gt;</span>
                        May 15 - Johnson Case
<span class="cstat-no" title="statement not covered" >                      &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                &lt;div className="bg-white rounded-lg p-3 shadow-sm border border-gray-50 hover:border-blue-100 transition-all duration-200"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="flex items-start"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="h-6 w-6 rounded bg-purple-50 flex items-center justify-center mr-2 mt-0.5"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;svg</span>
<span class="cstat-no" title="statement not covered" >                        xmlns="http://www.w3.org/2000/svg"</span>
<span class="cstat-no" title="statement not covered" >                        className="h-3.5 w-3.5 text-purple-500"</span>
<span class="cstat-no" title="statement not covered" >                        viewBox="0 0 20 20"</span>
<span class="cstat-no" title="statement not covered" >                        fill="currentColor"</span>
                      &gt;
<span class="cstat-no" title="statement not covered" >                        &lt;path</span>
<span class="cstat-no" title="statement not covered" >                          fillRule="evenodd"</span>
<span class="cstat-no" title="statement not covered" >                          d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"</span>
<span class="cstat-no" title="statement not covered" >                          clipRule="evenodd"</span>
<span class="cstat-no" title="statement not covered" >                        /&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;p className="text-sm font-medium text-gray-800"&gt;</span>
                        Court Hearing
<span class="cstat-no" title="statement not covered" >                      &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;p className="text-xs text-gray-400"&gt;</span>
                        May 20 - Rodriguez
<span class="cstat-no" title="statement not covered" >                      &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
&nbsp;
          {/* Bottom action button */}
<span class="cstat-no" title="statement not covered" >          &lt;div className="flex justify-end"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;button className="bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs font-medium px-4 py-2 rounded-lg hover:shadow-lg transform hover:translate-y-0.5 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-300 focus:ring-opacity-50"&gt;</span>
              Open Dashboard
<span class="cstat-no" title="statement not covered" >            &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/motion.div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/motion.div&gt;</span>
  );
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-28T18:05:11.306Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    